/*
 Navicat Premium Data Transfer

 Source Server         : IDC
 Source Server Type    : SQLite
 Source Server Version : 3035005 (3.35.5)
 Source Schema         : main

 Target Server Type    : SQLite
 Target Server Version : 3035005 (3.35.5)
 File Encoding         : 65001

 Date: 13/03/2025 21:00:31
*/

PRAGMA foreign_keys = false;

-- ----------------------------
-- Table structure for idc_device_log
-- ----------------------------
DROP TABLE IF EXISTS "idc_device_log";
CREATE TABLE idc_device_log (
	action TEXT, 
	"actionDesc" TEXT, 
	"approverAccount" TEXT, 
	"controlResult" TEXT, 
	"controlType" TEXT, 
	"controlUuid" TEXT, 
	"cooperateFinishedTime" TEXT, 
	"cooperateType" TEXT, 
	"dbName" TEXT, 
	"extendField2" TEXT, 
	"extendField3" TEXT, 
	"extendField5" TEXT, 
	"extendField6" TEXT, 
	"isSso" TEXT, 
	"logType" TEXT, 
	"operateTime" TEXT, 
	"operatorAccount" TEXT, 
	"operatorDept" TEXT, 
	"operatorFullDept" TEXT, 
	"operatorName" TEXT, 
	"platformIp" TEXT, 
	"platformType" TEXT, 
	protocol TEXT, 
	"requestIp" TEXT, 
	"resourceId" TEXT, 
	"resourceIp" TEXT, 
	"resourceName" TEXT, 
	"resourcePort" TEXT, 
	"resourceService" TEXT, 
	"resourceType" TEXT, 
	result TEXT, 
	"resultDesc" TEXT, 
	"slaveAccount" TEXT, 
	"tableName" TEXT, 
	"targetObject" TEXT, 
	"toolName" TEXT
);

PRAGMA foreign_keys = true;

