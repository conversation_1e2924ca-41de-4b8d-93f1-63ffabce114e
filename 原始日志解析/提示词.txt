请帮忙编写一个python程序，从本地sqlite数据库中执行SQL：“with log as (
select * from idc_device_log  where (actionDesc not like '%dis%' and actionDesc not like '%show%'
and actionDesc not like '%screen-length%' and actionDesc not like '%tftp%' and actionDesc not like '%ping%' and actionDesc not like '%view%'
and actionDesc not like '%dir%' and actionDesc not like '%monitor%'  and actionDesc not like '%copy%'
and actionDesc not like '%environment%' and actionDesc not like '%check%'
and trim(actionDesc) <>'' ) order by operateTime
)
select date(operateTime) operateDay,operatorName,operatorAccount,resourceIp,group_concat(operateTime||' '||actionDesc,char(10)) log_content
from log group by date(operateTime),operatorName,operatorAccount,resourceIp
having log_content like '%shutdown%'
;”，将SQL的内容写到pandas中，遍历pandas的每一行，根据log_content 调用deepseek的api分析用户做哪些端口开关操作，提示词是：
“
你是一名网络命令审核专家，能从一堆日志中找出哪些端口做了开关，直接返回最终打开了哪些端口，关闭了哪些端口，直接给出输出结果，无需解释。请注意，网络中的命令有可能是缩写：如undo shu是undo shutdown的缩写。如果同一端口多次打开关闭，输出最后一条命令。一般网络的命令是先进入端口，再对端口进行开关，仅进入端口没有明确操作的情况下不能识别为开关端口，也不要把对IP地址数据删除（如 undo ip address）识别为端口操作。结果输出格式:时间|动作|端口|具体对应的命令。
”，
然后解析deepseek返回结果，将结果写入都一个新的pandas中
pandas中每一行的字段包含 operateDay，operatorName，operatorAccount，resourceIp，port(端口)，operateTime(操作时间)，action（动作），command（具体命令）