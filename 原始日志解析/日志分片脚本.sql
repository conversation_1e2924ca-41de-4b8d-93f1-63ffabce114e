with log as (
select * from idc_device_log where resourceIp='**************' order by operateTime
)
select date(operateTime),operatorName,operatorAccount,resourceIp,group_concat(operateTime||' '||actionDesc,char(10))
from log group by date(operateTime),operatorName,operatorAccount,resourceIp;


with log as (
select * from idc_device_log  where (actionDesc not like '%dis%' and actionDesc not like '%show%'
and actionDesc not like '%screen-length%' and actionDesc not like '%tftp%' and actionDesc not like '%ping%' and actionDesc not like '%view%'
and actionDesc not like '%dir%' and actionDesc not like '%monitor%'  and actionDesc not like '%copy%'
and actionDesc not like '%environment%' and actionDesc not like '%check%'
and trim(actionDesc) <>'' ) order by operateTime
)
select date(operateTime),operatorName,operatorAccount,resourceIp,group_concat(operateTime||' '||actionDesc,char(10)) log_content
from log group by date(operateTime),operatorName,operatorAccount,resourceIp
having log_content like '%shutdown%'
;