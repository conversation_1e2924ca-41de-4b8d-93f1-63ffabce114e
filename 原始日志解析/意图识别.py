# Please install OpenAI SDK first: `pip3 install openai`

from openai import OpenAI
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

client = OpenAI(
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url="https://api.deepseek.com",  # DeepSeek兼容OpenAI的API端点
)


response = client.chat.completions.create(
    model="deepseek-chat",
    messages=[
        {"role": "system", "content": "你是一名网络命令审核专家，能从一堆日志中找出哪些端口做了开关，直接返回最终打开了哪些端口，关闭了哪些端口，直接给出输出结果，无需解释。请注意，网络中的命令有可能是缩写：如undo shu是undo shutdown的缩写。如果同一端口多次打开关闭，输出最后一条命令。一般网络的命令是先进入端口，再对端口进行开关，仅进入端口没有明确操作的情况下不能识别为开关端口，也不要把对IP地址数据删除（如 undo ip address）识别为端口操作。结果输出格式:时间|动作|端口|具体对应的命令。"},
        {"role": "user", "content": """
2025-03-01 00:21:26 display current-configuration 
2025-03-01 00:21:26 display current-configuration 
2025-03-01 00:22:04 display interface Eth-Trunk90 
2025-03-01 00:22:04 display interface Eth-Trunk90 
2025-03-01 00:23:27 display current-configuration interface 100GE 13/0/6 
2025-03-01 00:23:27 display current-configuration interface 100GE 13/0/6 
2025-03-01 00:24:24 interface 100GE 13/0/7 
2025-03-01 00:24:24 interface 100GE 13/0/7 
2025-03-01 00:24:28 description cT:1000001407230000-TianYiYun20240614 
2025-03-01 00:24:28 description cT:1000001407230000-TianYiYun20240614 
2025-03-01 00:24:40 eth-trunk 90 2025-03-01 00:24:40 eth-trunk 90 2025-03-01 00:24:46 display this 
2025-03-01 00:24:46 display this 
2025-03-01 00:25:31 eth-trunk 90 2025-03-01 00:25:31 eth-trunk 90 
2025-03-01 00:25:43 portswitch 
2025-03-01 00:25:43 portswitch 
2025-03-01 00:25:45 display this 
2025-03-01 00:25:45 display this 
2025-03-01 00:25:46 eth-trunk 90 2025-03-01 00:25:46 eth-trunk 90 
2025-03-01 00:25:51 device transceiver 100GBASE-FIBER 
2025-03-01 00:25:51 device transceiver 100GBASE-FIBER 
2025-03-01 00:26:10 display this 
2025-03-01 00:26:10 display this 
2025-03-01 00:26:13 undo shutdown 
2025-03-01 00:26:13 undo shutdown 
2025-03-01 00:28:33 display interface 100GE 13/0/7 
2025-03-01 00:28:33 display interface 100GE 13/0/7 
2025-03-01 00:28:41 display interface Eth-Trunk90 
2025-03-01 00:28:41 display interface Eth-Trunk90 
2025-03-01 00:28:46 display interface Eth-Trunk90 
2025-03-01 00:28:46 display interface Eth-Trunk90 
2025-03-01 00:28:48 display interface Eth-Trunk90 
2025-03-01 00:28:48 display interface Eth-Trunk90 
2025-03-01 00:28:55 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:28:55 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:31:27 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:31:27 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:31:29 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:31:29 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:35:30 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:35:30 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:35:32 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:35:32 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:43:49 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:43:49 display interface 100GE 13/0/7 transceiver verbose 
2025-03-01 00:43:53 display interface Eth-Trunk90 
2025-03-01 00:43:53 display interface Eth-Trunk90 
2025-03-01 00:47:06 display this 2025-03-01 00:47:06 display this 
2025-03-01 00:47:34 undo ip route-static ********** ************* *************** 
2025-03-01 00:47:34 undo ip route-static ********** ************* *************** 
2025-03-01 06:04:19  
2025-03-01 06:04:19
        """},
    ],
    stream=False
)

print(response.choices[0].message.content)