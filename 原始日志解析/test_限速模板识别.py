#!/usr/bin/env python3
# 测试限速模板识别脚本

import subprocess
import json

# 测试JSON数据
test_json = {
    "UnitInfo": {
        "Specialty": "IDC",
        "DevSet": "DEV_IDC_S_H3_S12508",
        "UuID": "",
        "UnitID": "2024010928925487104",
        "DevModel": "IDC_S12508",
        "ColTime": "20250508091100",
        "Traceid": "6e7a1664-c209-4c6a-aab9-2a465ad4976c",
        "Egion": "IDC",
        "MgmtIp": "************",
        "Vendor": "H3"
    },
    "SJB_IDC_ALL_CONF": {
        "data": "display cur\r\r\n#\r\r\n version 7.1.045, Release 1150\r\r\n#\r\r\nmdc Admin id 1\r\r\n#\r\r\n sysname NT-CQ-DSW-2.IDC.S12508F\r\r\n#\r\r\n clock timezone GMT add 08:00:00\r\r\n clock protocol none\r\r\n#\r\r\n telnet server enable\r\r\n telnet server acl 2577\r\r\n#\r\r\n traffic classifier xiansu-100M\r\r\n  if-match any\r\r\n traffic behavior xiansu-100M\r\r\n  car cir 100000 cbs 6250000 ebs 0 green pass yellow pass red discard\r\r\n qos policy xiansu-100M\r\r\n  classifier xiansu-100M behavior xiansu-100M\r\r\n#\r\r\n traffic classifier xiansu-200M\r\r\n  if-match any\r\r\n traffic behavior xiansu-200M\r\r\n  car cir 200000 cbs 12500000 ebs 0 green pass yellow pass red discard\r\r\n qos policy xiansu-200M\r\r\n  classifier xiansu-200M behavior xiansu-200M\r\r\n#\r\r\ninterface GigabitEthernet1/0/1\r\r\n description To_Customer_A\r\r\n qos apply policy xiansu-100M inbound\r\r\n#\r\r\ninterface GigabitEthernet1/0/2\r\r\n description To_Customer_B\r\r\n qos apply policy xiansu-200M inbound\r\r\n#\r\r\ninterface GigabitEthernet1/0/3\r\r\n description To_Customer_C\r\r\n qos car inbound cir 50000 cbs 3125000 ebs 0 green pass yellow pass red discard\r\r\n#"
    }
}

def main():
    """测试限速模板识别脚本"""
    print("开始测试限速模板识别脚本...")
    
    # 将测试JSON转换为字符串
    json_str = json.dumps(test_json)
    
    try:
        # 调用限速模板识别脚本
        result = subprocess.run(
            ["python3", "原始日志解析/限速模板识别.py", json_str],
            capture_output=True,
            text=True,
            check=True
        )
        
        # 打印结果
        print("\n=== 脚本输出 ===")
        print(result.stdout)
        
        if result.stderr:
            print("\n=== 错误输出 ===")
            print(result.stderr)
            
        print("\n测试完成!")
    except subprocess.CalledProcessError as e:
        print(f"\n脚本执行失败，错误代码: {e.returncode}")
        print(f"标准输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
