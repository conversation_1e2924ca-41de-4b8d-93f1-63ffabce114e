import sqlite3
import pandas as pd
import requests
import re
from datetime import datetime

from dotenv import load_dotenv
import os
from openai import OpenAI

# 加载环境变量
load_dotenv()


# 替换为你的Deepseek API信息
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_API_URL = "https://api.deepseek.com"

client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url=DEEPSEEK_API_URL)


def analyze_log_content(log_content):
    """调用Deepseek API分析日志内容"""


    try:
        #print(log_content)
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system",
                 "content": "你是一名网络命令审核专家，能从一堆日志中找出哪些端口做了开关，直接返回最终打开了哪些端口，关闭了哪些端口，直接给出输出结果，无需解释。请注意，网络中的命令有可能是缩写：如undo shu是undo shutdown的缩写。如果同一端口多次打开关闭，输出最后一条命令。一般网络的命令是先进入端口，再对端口进行开关，仅进入端口没有明确操作的情况下不能识别为开关端口，也不要把对IP地址数据删除（如 undo ip address）识别为端口操作。结果输出格式:时间|动作|端口|具体对应的命令。"},
                {"role": "user", "content": log_content},
            ],
            stream=False
        )
        result = response.choices[0].message.content
        print(result)
        return parse_api_result(result)
    except Exception as e:
        print(f"API调用失败: {str(e)}")
        return []


def parse_api_result(result_text):
    """解析API返回结果"""
    parsed_data = []
    # 使用正则表达式匹配结果格式
    pattern = r"(.*?)\|(\w+)\|(.*?)\|(.*)"

    for line in result_text.split('\n'):
        if not line.strip():
            continue
        match = re.match(pattern, line)
        if match:
            try:
                # 转换时间格式（根据实际日志格式调整）
                raw_time = match.group(1)
                operate_time = raw_time

                parsed_data.append({
                    "operateTime": operate_time,
                    "action": match.group(2).lower(),  # 统一小写
                    "port": match.group(3).strip(),
                    "command": match.group(4).strip()
                })
            except Exception as e:
                print(f"解析行失败: {line} | 错误: {str(e)}")
    return parsed_data


def main():
    # 连接数据库执行SQL
    conn = sqlite3.connect('IDC.db')  # 替换数据库路径
    query = """
    with log as (
        select * from idc_device_log  
        where (actionDesc not like '%dis%' and actionDesc not like '%show%'
            and actionDesc not like '%screen-length%' and actionDesc not like '%tftp%' 
            and actionDesc not like '%ping%' and actionDesc not like '%view%'
            and actionDesc not like '%dir%' and actionDesc not like '%monitor%'  
            and actionDesc not like '%copy%'
            and actionDesc not like '%environment%' and actionDesc not like '%check%'
            and trim(actionDesc) <>'' ) 
        order by operateTime
    )
    select date(operateTime) operateDay,
           operatorName,
           operatorAccount,
           resourceIp,
           group_concat(operateTime||' '||actionDesc, char(10)) as log_content
    from log 
    group by date(operateTime), operatorName, operatorAccount, resourceIp
    having log_content like '%shutdown%'
    """

    # 读取数据到DataFrame
    raw_df = pd.read_sql_query(query, conn)
    conn.close()

    # 准备结果DataFrame
    result_list = []

    # 遍历每一行记录
    for idx, row in raw_df.iterrows():
        # 调用API分析日志内容
        analysis_results = analyze_log_content(row['log_content'])
        print(f"第{idx}行,结果{analysis_results}")
        # 合并分析结果
        for result in analysis_results:
            result_list.append({
                "operateDay": row['operateDay'],
                "operatorName": row['operatorName'],
                "operatorAccount": row['operatorAccount'],
                "resourceIp": row['resourceIp'],
                "port": result['port'],
                "operateTime": result['operateTime'],
                "action": result['action'],
                "command": result['command']
            })

    # 创建结果DataFrame
    result_df = pd.DataFrame(result_list)

    # 重新连接数据库保存结果
    new_conn = sqlite3.connect('IDC.db')

    # 使用to_sql写入新表
    result_df.to_sql(
        name='idc_device_log_action',
        con=new_conn,
        if_exists='replace',  # 可根据需要改为'append'
        index=False,
        dtype={
            'operateDay': 'DATE',
            'operatorName': 'TEXT',
            'operatorAccount': 'TEXT',
            'resourceIp': 'TEXT',
            'port': 'TEXT',
            'operateTime': 'DATETIME',
            'action': 'TEXT',
            'command': 'TEXT'
        }
    )

    new_conn.close()
    print("分析完成，结果已保存到idc_device_log_action表")


if __name__ == "__main__":
    main()