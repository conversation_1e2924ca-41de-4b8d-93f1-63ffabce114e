import csv
import json
import pandas as pd
import traceback
from sqlalchemy import create_engine


def csv_to_sqlite(input_csv, db_name):
    try:
        data = []
        all_columns = set()

        # 读取CSV文件
        with open(input_csv, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            headers = next(reader)  # 跳过标题行
            print(f"CSV标题行: {headers}")

            # 处理每一行数据
            for row_num, row in enumerate(reader, start=2):  # 行号从2开始（跳过标题）
                try:
                    if not row:
                        print(f"第 {row_num} 行为空，已跳过")
                        continue

                    # 清洗JSON字符串
                    json_str = row[0].strip('"')
                    # print("准备解析：",json_str)
                    if not json_str:
                        print(f"第 {row_num} 行无有效JSON数据，已跳过")
                        continue

                    # 解析JSON
                    item = json.loads(json_str)
                    data.append(item)
                    all_columns.update(item.keys())

                except json.JSONDecodeError as e:
                    print(f"JSON解析失败 [第 {row_num} 行]")
                    print(f"错误内容: {e}")
                    print(f"原始数据: {row[0][:50]}...")  # 显示前50字符避免日志过长
                    print("跟踪信息:", traceback.format_exc())
                except Exception as e:
                    print(f"处理第 {row_num} 行时发生未知错误: {str(e)}")

        # 生成DataFrame
        if not data:
            raise ValueError("无有效数据可导出，请检查输入文件内容")

        columns = sorted(all_columns)
        df = pd.DataFrame(data, columns=columns).fillna('')

        # 写入Excel
        engine = create_engine("sqlite:///"+db_name)
        df.to_sql(
            name="idc_device_log",
            con=engine,
            if_exists="replace",
            index=False,
            chunksize=1000
        )
        print(f"转换成功！输出文件: {db_name}")

    except FileNotFoundError:
        print(f"错误: 输入文件 {input_csv} 不存在")
    except PermissionError:
        print(f"错误: 无权限写入文件 {db_name}")
    except Exception as e:
        print(f"程序运行失败: {str(e)}")
        print("详细跟踪信息:\n", traceback.format_exc())


# 使用示例
csv_to_sqlite("IDC.csv", "IDC.db")