import requests

url="http://daas.oss.telecomjs.com:39042/interface?api_code=query_ip_man_config_file&sharding_code=ds_net_resource_js&version=V20220730150749285"
headers={
    "app_id":"wzrh_inoc"
}
data={}
data["start"]='2025-03-18 00:00:00'
data["end"]='2025-03-19 00:00:00'
data["manage_ip"]='***********'
data["config_type_id"]="80100052"
data["currentPage"]=1
data["pageSize"]=1000
data["specialty"]="IDC"

value=requests.post(url=url,headers=headers,json=data).json()["response"]["data"][0]["file_content"]
print(value)