# Please install OpenAI SDK first: `pip3 install openai`

from openai import OpenAI
from dotenv import load_dotenv
import os
import json
import sys
from typing import List, Dict, Any

# 加载环境变量
load_dotenv()


client = OpenAI(
    api_key="sk-91e74fbe255746258096aeaea2060a5e",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # DeepSeek兼容OpenAI的API端点
)

def extract_data_from_json(json_input):
    """
    从输入的JSON中提取SJB_IDC_ALL_CONF->data字段

    Args:
        json_input (str): JSON格式的输入字符串

    Returns:
        str: 提取出的data字段内容，如果提取失败则返回None
    """
    try:
        # 解析JSON
        data = json.loads(json_input)

        # 检查并提取SJB_IDC_ALL_CONF->data字段
        if "IDC_CaiJi03_SHEBEI_telnet_ALL" in data:
            return data["IDC_CaiJi03_SHEBEI_telnet_ALL"]
        else:
            print("错误: 输入JSON中未找到SJB_IDC_ALL_CONF->data字段")
            return None
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {str(e)}")
        return None
    except Exception as e:
        print(f"错误: 提取数据时发生未知错误 - {str(e)}")
        return None

def split_text_into_chunks(text: str, chunk_size: int = 500, overlap: int = 100) -> List[str]:
    """
    将文本分割成指定大小的块，每个块向后多取一定行数作为重叠部分

    Args:
        text (str): 要分割的文本
        chunk_size (int): 每个块的基本行数
        overlap (int): 每个块与下一个块的重叠行数

    Returns:
        List[str]: 分割后的文本块列表
    """
    lines = text.split('\n')
    chunks = []

    # 如果总行数小于chunk_size，直接返回整个文本
    if len(lines) <= chunk_size:
        return [text]

    # 分割文本
    i = 0
    while i < len(lines):
        # 计算当前块的结束位置
        end = min(i + chunk_size, len(lines))

        # 计算重叠部分的结束位置
        overlap_end = min(end + overlap, len(lines))

        # 提取当前块（包括重叠部分）
        chunk = '\n'.join(lines[i:overlap_end])
        chunks.append(chunk)

        # 移动到下一个块的起始位置
        i += chunk_size

    return chunks

def analyze_config_chunk(chunk: str) -> str:
    """
    使用大模型分析配置块

    Args:
        chunk (str): 配置文本块

    Returns:
        str: 大模型的分析结果
    """
    response = client.chat.completions.create(
        model="deepseek-v3",
        messages=[
            {"role": "system", "content": """你是一名网络设备配置审核专家，能够从网络设备中分析出设备有哪些限速模板以及端口的限速。
             请严格按照以下格式返回结果：

             限速模板：
             模板名称|模板限速值(单位bps)|具体配置命令

             端口限速：
             端口名称|方向|模板|限速值(单位bps)

             注意：
             1. 如果端口不是通过模板限速，而是直接配置限速规则，则模板字段填写为空
             2. 限速值必须转换为bps单位（如25Gbps = 25000000000bps）
             3. 请使用简单的|分隔格式，不要使用markdown表格格式
             """},
            {"role": "user", "content": chunk},
        ],
        temperature=0,
        stream=False
    )

    return response.choices[0].message.content

def merge_results(results: List[str]) -> Dict[str, List[str]]:
    """
    合并多个分析结果

    Args:
        results (List[str]): 多个分析结果的列表

    Returns:
        Dict[str, List[str]]: 合并后的结果，包含限速模板和端口限速
    """
    # 初始化结果字典
    merged = {
        "限速模板": set(),
        "端口限速": set()
    }

    # 处理每个结果
    for result in results:
        # 分割结果中的行
        lines = result.strip().split('\n')

        # 标记当前处理的部分（限速模板或限速端口）
        current_section = None
        in_table = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 跳过markdown表格分隔线
            if line.startswith('|') and all(c in '|-: ' for c in line):
                continue

            # 检查是否是标题行
            if "限速模板" in line:
                current_section = "限速模板"
                in_table = False
                continue
            elif "端口限速" in line:
                current_section = "端口限速"
                in_table = False
                continue

            # 检查是否是表格标题行
            if current_section and "|" in line and ("模板名称" in line or "端口名称" in line):
                in_table = True
                continue

            # 如果是数据行且已确定当前部分，则添加到相应集合
            if current_section and "|" in line and not line.startswith('#'):
                # 处理markdown表格格式的数据
                if in_table or line.count('|') >= 3:
                    # 清理表格格式，移除首尾的|
                    clean_line = line.strip('|').strip()
                    if clean_line and not clean_line.startswith('-'):
                        # 将表格格式转换为简单的|分隔格式
                        parts = [part.strip() for part in clean_line.split('|')]
                        if len(parts) >= 3 and parts[0] and parts[0] != '---':
                            # 标准化方向字段
                            if len(parts) >= 2:
                                if parts[1] in ['出向', 'outbound']:
                                    parts[1] = 'outbound'
                                elif parts[1] in ['入向', 'inbound']:
                                    parts[1] = 'inbound'

                            # 清理限速值格式
                            if len(parts) >= 4 and parts[3]:
                                parts[3] = parts[3].replace('bps', '').strip()
                                if parts[3] and not parts[3].endswith('bps') and '未指定' not in parts[3]:
                                    parts[3] = parts[3] + 'bps'

                            formatted_line = '|'.join(parts)
                            merged[current_section].add(formatted_line)
                else:
                    # 处理简单的|分隔格式
                    merged[current_section].add(line)

    # 将集合转换为列表并排序
    return {
        "限速模板": sorted(list(merged["限速模板"])),
        "端口限速": sorted(list(merged["端口限速"]))
    }

def main():
    # 从device_config.json文件读取配置
    try:
        with open('速率模板解析/device_config.json', 'r', encoding='utf-8') as file:
            json_input = file.read()
    except FileNotFoundError:
        print("错误: device_config.json文件不存在")
        return
    except Exception as e:
        print(f"读取配置文件时发生错误: {str(e)}")
        return

    # 提取data字段
    config_data = extract_data_from_json(json_input)

    if not config_data:
        print("无法提取配置数据，程序退出")
        return

    # 将\r\n替换为实际的换行符
    config_data = config_data.replace('\\r\\n', '\n')

    # 计算并打印配置数据的行数
    lines_count = len(config_data.split('\n'))
    print(f"配置数据总行数: {lines_count}")

    # 将配置数据分割成块
    chunks = split_text_into_chunks(config_data, chunk_size=500, overlap=100)
    print(f"配置数据已分割成 {len(chunks)} 个块")

    # 分析每个块并收集结果
    all_results = []
    for i, chunk in enumerate(chunks):
        print(f"正在分析第 {i+1}/{len(chunks)} 个块...")
        result = analyze_config_chunk(chunk)
        all_results.append(result)
        print(f"第 {i+1} 个块分析完成")

    # 合并所有结果
    print("\n===== 各块分析结果 =====")
    for i, result in enumerate(all_results):
        print(f"\n----- 块 {i+1} 的结果 -----")
        print(result)

    # 使用merge_results函数合并所有结果
    merged = merge_results(all_results)

    # 输出合并后的结果
    print("\n===== 合并后的最终结果 =====")

    print("\n## 限速模板")
    if merged["限速模板"]:
        print("| 模板名称 | 模板限速值(bps) | 具体配置命令 |")
        print("|----------|-----------------|--------------|")
        for template in merged["限速模板"]:
            parts = template.split('|')
            if len(parts) >= 3:
                print(f"| {parts[0].strip()} | {parts[1].strip()} | {parts[2].strip()} |")
            else:
                print(f"| {template} | | |")
    else:
        print("未找到限速模板")

    print("\n## 端口限速")
    if merged["端口限速"]:
        print("| 端口名称 | 方向 | 模板 | 限速值(bps) |")
        print("|----------|------|------|-------------|")

        # 处理重复端口，保留最后一个配置
        port_dict = {}
        for port in merged["端口限速"]:
            parts = port.split('|')
            if len(parts) >= 4:
                port_key = f"{parts[0].strip()}|{parts[1].strip()}"
                port_dict[port_key] = port

        # 按端口名称排序输出
        for port in sorted(port_dict.values()):
            parts = port.split('|')
            if len(parts) >= 4:
                print(f"| {parts[0].strip()} | {parts[1].strip()} | {parts[2].strip()} | {parts[3].strip()} |")
            elif len(parts) == 3:
                print(f"| {parts[0].strip()} | {parts[1].strip()} | {parts[2].strip()} | |")
            else:
                print(f"| {port} | | | |")
    else:
        print("未找到端口限速")

if __name__ == "__main__":
    main()