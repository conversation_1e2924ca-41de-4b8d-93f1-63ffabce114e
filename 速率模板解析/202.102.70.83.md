===== 合并后的最终结果 =====

## 限速模板
| 模板名称 | 模板限速值(Mbps) | 具体配置命令 |
|----------|------------------|--------------|
| --- | --- | --- |
| xiansu-10G | 10240 | `qos car xiansu-10G cir 10240000 kbps cbs 536870912 bytes` |
| xiansu-1G | 1024 | `qos car xiansu-1G cir 1024000 kbps cbs 204800000 bytes` |
| xiansu-50G | 51200 | `qos car xiansu-50G cir 51200000 kbps cbs 536870912 bytes` |

## 端口限速
| 端口名称 | 方向 | 模板 | 限速值(Mbps) |
|----------|------|------|--------------|
| --- | --- | --- | --- |
| Eth-Trunk3 | inbound | xiansu-1G | 1024 |
| Eth-Trunk3 | outbound | Tencent-deny-udp-xiansu-1G | 1024 |
| Eth-Trunk4 | inbound | xiansu-1G | 1024 |
|  |  100GE1/0/0     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE1/0/1     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE2/0/0     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE2/0/1     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE3/0/0     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE3/0/1     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE6/0/0     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE6/0/1     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE7/0/0     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE7/0/1     |  队列整形  |  `qos queue 4 shaping cir 25 gbps pir 25 gbps`  |  |
|  |  100GE8/0/1     |  outbound   |  -     |  1024000 (1Tbps)   |  `qos lr cir 1024000 kbps cbs 204800000 bytes outbound`  |  |
|  | --------------- | ----------- | ------ | ------------------ | -------------- |  |
|  | --------------- | ---------- | ---------- |  |