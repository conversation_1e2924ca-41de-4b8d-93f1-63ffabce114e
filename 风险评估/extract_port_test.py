# Please install OpenAI SDK first: `pip3 install openai`

from openai import OpenAI

client = OpenAI(api_key="sk-1fb0be208dd84e9887aef4086f1ceea8", base_url="https://api.deepseek.com")

response = client.chat.completions.create(
    model="deepseek-chat",
    messages=[
        {"role": "system", "content": "你是一个网络设备命令解析器，只需从用户输入中提取端口编码（如6/1/14/Ten-GigabitEthernet5/0/10等格式），直接返回端口值，不要任何解释。"},
        {"role": "user", "content": " shutdown 命令关闭接口 10GE16/0/8, 关联命令：interface 10GE16/0/8"},
    ],
    stream=False
)

print(response.choices[0].message.content)