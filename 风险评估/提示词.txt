我想开发一个基于deepseek的智能体，能够支持输入对网络设备的操作命令，从命令中得到端口的编码：
如 输入 “开启接口 configure port 6/1/14 shutdown 命令”，得到“6/1/14”
输入“开启接口 6/1/14 命令”，得到“6/1/14”
输入“ undo shutdown 命令开启接口 Ten-GigabitEthernet5/0/10, 关联命令：interface Ten-GigabitEthernet5/0/10” 得到 “Ten-GigabitEthernet5/0/10”
输入 “shutdown 命令关闭接口 100GE5/15/0/0, 关联命令：interface 100GE5/15/0/0” 得到 “100GE5/15/0/0”
输入“ no shutdown 命令开启接口 cgei-1/15/1/2, 关联命令：interface cgei-1/15/1/2”得到 “cgei-1/15/1/2”