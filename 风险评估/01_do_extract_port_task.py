import os
import time
from concurrent.futures import ThreadPoolExecutor

import pandas as pd
from openai import OpenAI
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

client = OpenAI(
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url="https://api.deepseek.com/v1",  # DeepSeek兼容OpenAI的API端点
)

INPUT_FILE = "IDC端口操作日志.xlsx"
OUTPUT_FILE = "IDC端口操作日志_带端口编码_DeepSeek.xlsx"


def get_port_code(command: str) -> str:
    """使用DeepSeek的OpenAI兼容API获取端口编码"""
    try:
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {
                    "role": "system",
                    "content": "你是一个网络设备命令解析专家，请严格：\n"
                               "1. 仅输出端口编码（如：6/1/14、Ten-GigabitEthernet5/0/10）\n"
                               "2. 不要任何解释或标点\n"
                               "3. 若无匹配项输出'未识别'"
                },
                {
                    "role": "user",
                    "content": command
                }
            ],
            temperature=0.1,
            max_tokens=30,
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"API请求失败: {str(e)}")
        return "API错误"

def append_port_code(df: pd.DataFrame, idx, cmd):
    port_code = get_port_code(cmd)
    df.at[idx, '端口编码'] = port_code
    result = [idx, port_code]
    print(result)
    return result

def process_spreadsheet():
    # 读取Excel文件
    df = pd.read_excel(INPUT_FILE, engine='openpyxl')

    # 添加新列
    df['端口编码'] = ""

    # 批量处理命令
    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = []
        for idx, row in df.iterrows():
            raw_command = str(row['操作命令'])

            futures.append(executor.submit(append_port_code,df,idx, raw_command))
            # 调用API并记录结果
            # df.at[idx, '端口编码'] = get_port_code(raw_command)

            # 打印进度并控制请求频率
            # print(f"已处理 {idx + 1}/{len(df)} 条 | 最新结果: {df.at[idx, '端口编码']}")
            time.sleep(0.1)  # 根据DeepSeek的速率限制调整
        for future in futures:
            pass
    # 保存结果
    with pd.ExcelWriter(OUTPUT_FILE, engine='openpyxl') as writer:
        df.to_excel(writer, index=False)

        # 自动调整列宽
        worksheet = writer.sheets['Sheet1']
        for column in worksheet.columns:
            max_length = max(len(str(cell.value)) for cell in column)
            worksheet.column_dimensions[column[0].column_letter].width = max_length + 2


if __name__ == "__main__":
    # 环境验证
    if not os.getenv("DEEPSEEK_API_KEY"):
        raise ValueError("请创建.env文件并设置DEEPSEEK_API_KEY")

    # 执行处理
    process_spreadsheet()
    print(f"处理完成，结果已保存至 {OUTPUT_FILE}")