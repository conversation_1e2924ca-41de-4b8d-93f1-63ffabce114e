import pandas as pd
from datetime import datetime
import re

# 读取操作日志和开通订单数据
operation_log = pd.read_excel('IDC端口操作日志.xlsx')
orders = pd.read_excel('IDC开通订单.xlsx')

# 数据预处理
# 转换时间格式
operation_log['时间'] = pd.to_datetime(operation_log['时间'], utc=True).dt.tz_convert(None)
orders['收单时间'] = pd.to_datetime(orders['收单时间'])
orders['完成时间'] = pd.to_datetime(orders['完成时间'])

# 提取关键信息
operation_log['设备IP'] = operation_log['设备IP'].astype(str).str.strip()

# 定义一个函数来解析端口编码


# 应用函数解析端口编码
operation_log['操作类型'] = operation_log['备注']

orders['预受理端口对应设备ip'] = orders['预受理端口对应设备ip'].astype(str)
orders['端口'] = orders['预受理端口名称']

# 匹配逻辑
results = []
for _, log in operation_log.iterrows():
    matched_order = None
    for _, order in orders.iterrows():
        # 设备IP匹配
        if log['设备IP'] == order['预受理端口对应设备ip']:
            # 端口匹配
            if log['端口'] == order['端口']:


                # 操作类型匹配
                if (log['操作类型'] == '打开端口' and order['动作'] in ['新装','变更']) or \
                   (log['操作类型'] == '关闭端口' and order['动作'] in ['拆机','变更']):
                    matched_order = order
                    break
                    # 时间匹配
                    '''
                    if log['时间'] >= order['收单时间'] and log['时间'] <= order['完成时间']:
                        matched_order = order
                        break
                    '''

    # 准备结果
    result = log.to_dict()
    if matched_order is not None:
        result['订单号'] = matched_order['订单号']
        result['动作'] = matched_order['动作']
        result['业务编码'] = matched_order['业务编码']
        result['受理时间'] = matched_order['收单时间']
        result['完成时间'] = matched_order['完成时间']
        result['状态'] = matched_order['状态']
        result['预受理端口名称'] = matched_order['预受理端口名称']
        result['匹配结果'] = '按单施工'
    else:
        result['订单号'] = None
        result['动作'] = None
        result['业务编码'] = None
        result['受理时间'] = None
        result['完成时间'] = None
        result['状态'] = None
        result['预受理端口名称'] = None
        result['匹配结果'] = '未按单施工'
    results.append(result)

# 输出结果到Excel
results_df = pd.DataFrame(results)
results_df.to_excel('IDC日志审计结果.xlsx', index=False)
print("结果已成功输出到 'IDC日志审计结果.xlsx' 文件中。")