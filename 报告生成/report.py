import json
import requests
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv("DEEPSEEK_API_KEY")
print(api_key)

# 读取JSON文件
with open("data.json", "r", encoding="utf-8") as f:
    data = json.load(f)

# 生成风险描述的函数
def generate_risk_description(risk_title):
    response = requests.post(
        "https://api.deepseek.com/v1/chat/completions",
        headers={"Authorization": "Bearer "+api_key},
        json={
            "messages": [{
                "role": "user",
                "content": f"生成一段关于'{risk_title}'的风险描述，需包含现象和风险说明，语言简洁专业"
            }],
            "model": "deepseek-chat"
        }
    )
    return response.json()["choices"][0]["message"]["content"]

# 遍历JSON中的风险列表，为每个风险项生成描述
for risk in data["risks"]:
    title = risk["title"]
    description = generate_risk_description(title)
    risk["description"] = description  # 将生成的描述添加到JSON中

# 保存更新后的JSON（可选）
with open("data_with_descriptions.json", "w", encoding="utf-8") as f:
    json.dump(data, f, ensure_ascii=False, indent=2)